@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Open+Sans:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222 47% 11%;
    --primary-foreground: 210 40% 98%;

    --secondary: 345 59% 26%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 43 74% 49%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-body;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-heading font-bold;
  }
}

.sanskrit-quote {
  font-family: 'Noto Sans Devanagari', sans-serif;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8), 0 4px 12px rgba(0, 0, 0, 0.5);
}

.text-shadow-strong {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 1), 0 2px 8px rgba(0, 0, 0, 0.8),
    0 4px 16px rgba(0, 0, 0, 0.6);
}

.section-heading {
  @apply text-xl md:text-2xl font-bold mb-6 text-gyaan-navy relative pb-3;
}

.section-heading::after {
  content: '';
  @apply absolute bottom-0 left-0 w-16 h-1 bg-gyaan-maroon;
}

/* Special styling for Impact and MoU Partners headings */
.impact-partners-heading-container {
  @apply relative flex flex-col items-center justify-center mb-8;
}

.impact-partners-heading-bg {
  @apply bg-gyaan-gold/90 py-6 px-5 rounded-lg shadow-lg mb-3 relative overflow-hidden;
}

.impact-partners-heading-bg::before {
  content: '';
  @apply absolute inset-0 bg-gyaan-gold/90 z-10;
}

.section-heading-impact-partners {
  @apply text-xl md:text-2xl lg:text-3xl font-extrabold text-center relative z-20;
  /* White text for maximum contrast */
  color: white;
  /* No gradient text effect - using solid white for better visibility */
  -webkit-text-fill-color: white;
  text-fill-color: white;
  /* Strong text shadow for better visibility against any background */
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.6), 0 3px 6px rgba(0, 0, 0, 0.4);
  animation: slide-up 0.8s ease-out forwards;
}

.impact-partners-heading-accent {
  @apply relative inline-block;
}

.impact-partners-heading-accent::before {
  content: '';
  @apply absolute -bottom-2 left-0 h-2 bg-gyaan-gold rounded-full;
  width: 0;
  animation: expand-line 1s ease-out 0.4s forwards;
  box-shadow: 0 0 8px rgba(213, 160, 33, 0.7);
  z-index: 10;
}

.impact-partners-heading-decoration {
  @apply flex justify-center mt-3 space-x-2;
}

.impact-partners-heading-dot {
  @apply w-3 h-3 rounded-full opacity-0;
  animation: fade-in-scale 0.5s ease-out forwards;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5), 0 0 4px rgba(0, 0, 0, 0.3);
}

.impact-partners-heading-dot:nth-child(1) {
  @apply bg-gyaan-navy border-2 border-white/70;
  animation-delay: 0.6s;
}

.impact-partners-heading-dot:nth-child(2) {
  @apply bg-gyaan-maroon border-2 border-white/70;
  animation-delay: 0.8s;
}

.impact-partners-heading-dot:nth-child(3) {
  @apply bg-gyaan-gold border-2 border-white/70;
  animation-delay: 1s;
}

.section-subheading {
  @apply text-lg md:text-xl font-semibold mb-4 text-gyaan-maroon;
}

.btn-primary {
  @apply bg-gyaan-navy text-white px-6 py-2 rounded-md hover:opacity-90 transition-all btn-glow-navy;
}

.btn-secondary {
  @apply bg-gyaan-maroon text-white px-6 py-2 rounded-md hover:opacity-90 transition-all btn-glow-maroon;
}

.btn-accent {
  @apply bg-gyaan-gold text-white px-6 py-2 rounded-md hover:opacity-90 transition-all btn-glow-gold;
}

/* Accessibility Styles */
.high-contrast {
  --background: 0 0% 100%;
  --foreground: 0 0% 0%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 0%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 0%;
  --primary: 0 0% 0%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 0%;
  --secondary-foreground: 0 0% 100%;
  --muted: 0 0% 90%;
  --muted-foreground: 0 0% 0%;
  --accent: 0 0% 0%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 100% 50%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 0%;
  --input: 0 0% 0%;
  --ring: 0 0% 0%;
}

.high-contrast * {
  border-color: black !important;
}

.high-contrast a:focus,
.high-contrast button:focus,
.high-contrast input:focus,
.high-contrast select:focus,
.high-contrast textarea:focus {
  outline: 3px solid yellow !important;
  outline-offset: 2px !important;
}

.font-size-normal {
  font-size: 16px;
}

.font-size-large {
  font-size: 18px;
}

.font-size-x-large {
  font-size: 20px;
}

.reduced-motion * {
  transition: none !important;
  animation: none !important;
}

/* Skip to content link - hidden until focused */
.skip-to-content {
  @apply sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:p-4 focus:bg-white focus:text-gyaan-navy focus:shadow-lg focus:rounded-md;
}

/* Focus styles for better accessibility */
:focus-visible {
  @apply outline-2 outline-offset-2 outline-gyaan-navy;
}

/* Improved form field accessibility */
input:focus,
select:focus,
textarea:focus {
  @apply ring-2 ring-gyaan-navy ring-offset-2;
}

/* Improved button accessibility */
button:focus-visible {
  @apply outline-2 outline-offset-2 outline-gyaan-navy;
}

/* Slider animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes slideIn {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in {
  animation: slideIn 0.5s ease forwards;
}

.animate-fade-in {
  animation: fadeIn 1s ease forwards;
}

/* Scroll animations */
.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-on-scroll.animate-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Blur effect for content backgrounds */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px); /* Safari support */
}

.backdrop-blur-md {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px); /* Safari support */
}

.backdrop-blur-lg {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px); /* Safari support */
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(4px)) {
  .backdrop-blur-sm {
    background-color: rgba(0, 0, 0, 0.6) !important;
  }

  .backdrop-blur-md {
    background-color: rgba(0, 0, 0, 0.7) !important;
  }

  .backdrop-blur-lg {
    background-color: rgba(0, 0, 0, 0.8) !important;
  }
}

/* Fancy heading animations */
@keyframes slide-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes expand-line {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 1;
  }
}

@keyframes fade-in-scale {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Pulsing glow effect for buttons */
@keyframes pulse-glow-gold {
  0% {
    box-shadow: 0 0 0 0 rgba(213, 160, 33, 0.3);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(213, 160, 33, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(213, 160, 33, 0);
  }
}

@keyframes pulse-glow-navy {
  0% {
    box-shadow: 0 0 0 0 rgba(10, 36, 99, 0.3);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(10, 36, 99, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(10, 36, 99, 0);
  }
}

@keyframes pulse-glow-maroon {
  0% {
    box-shadow: 0 0 0 0 rgba(123, 8, 40, 0.3);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(123, 8, 40, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(123, 8, 40, 0);
  }
}

/* Default glow effects for buttons */
.btn-glow-gold {
  box-shadow: 0 0 8px 0 rgba(213, 160, 33, 0.3);
  animation: pulse-glow-gold 3s infinite;
}

.btn-glow-navy {
  box-shadow: 0 0 8px 0 rgba(10, 36, 99, 0.3);
  animation: pulse-glow-navy 3s infinite;
}

.btn-glow-maroon {
  box-shadow: 0 0 8px 0 rgba(123, 8, 40, 0.3);
  animation: pulse-glow-maroon 3s infinite;
}

/* Enhanced hover glow effects */
.btn-glow-gold:hover {
  box-shadow: 0 0 15px 0 rgba(213, 160, 33, 0.5);
  animation: pulse-glow-gold 2s infinite;
}

.btn-glow-navy:hover {
  box-shadow: 0 0 15px 0 rgba(10, 36, 99, 0.5);
  animation: pulse-glow-navy 2s infinite;
}

.btn-glow-maroon:hover {
  box-shadow: 0 0 15px 0 rgba(123, 8, 40, 0.5);
  animation: pulse-glow-maroon 2s infinite;
}

/* Legacy class for backward compatibility */
.hover\:glow-button:hover {
  box-shadow: 0 0 15px 0 rgba(213, 160, 33, 0.5);
  animation: pulse-glow-gold 2s infinite;
}

/* Fancy section heading */
.fancy-heading-container {
  @apply relative flex flex-col items-center justify-center mb-12;
}

.fancy-heading {
  @apply text-xl md:text-2xl lg:text-3xl font-extrabold text-center relative z-20;
  /* White text for maximum contrast */
  color: white;
  /* No gradient text effect - using solid white for better visibility */
  -webkit-text-fill-color: white;
  text-fill-color: white;
  /* Strong text shadow for better visibility against any background */
  text-shadow: 0 2px 3px rgba(0, 0, 0, 0.6), 0 3px 6px rgba(0, 0, 0, 0.4);
  animation: slide-up 0.8s ease-out forwards;
}

/* Section-specific heading styles */
.about-heading-container .fancy-heading-accent::before {
  @apply bg-gyaan-navy;
}

.how-it-works-heading-container .fancy-heading-accent::before {
  @apply bg-gyaan-maroon;
}

/* Fallback for browsers or assistive technologies that don't support text-shadow */
@supports not (text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5)) {
  .fancy-heading {
    -webkit-text-stroke: 1px rgba(0, 0, 0, 0.3);
    text-stroke: 1px rgba(0, 0, 0, 0.3);
  }
}

.fancy-heading-accent {
  @apply relative inline-block;
}

.fancy-heading-accent::before {
  content: '';
  @apply absolute -bottom-2 left-0 h-2 bg-gyaan-gold rounded-full;
  width: 0;
  animation: expand-line 1s ease-out 0.4s forwards;
  box-shadow: 0 0 8px rgba(213, 160, 33, 0.7);
  z-index: 10;
}

.fancy-heading-decoration {
  @apply flex justify-center mt-3 space-x-2;
}

.fancy-heading-dot {
  @apply w-3 h-3 rounded-full opacity-0;
  animation: fade-in-scale 0.5s ease-out forwards;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5), 0 0 4px rgba(0, 0, 0, 0.3);
}

.fancy-heading-dot:nth-child(1) {
  @apply bg-gyaan-navy border-2 border-white/70;
  animation-delay: 0.6s;
}

.fancy-heading-dot:nth-child(2) {
  @apply bg-gyaan-maroon border-2 border-white/70;
  animation-delay: 0.8s;
}

.fancy-heading-dot:nth-child(3) {
  @apply bg-gyaan-gold border-2 border-white/70;
  animation-delay: 1s;
}

/* Section-specific styles */
/* About Section */
.about-section {
  @apply relative overflow-hidden bg-gyaan-navy/10;
}

.about-card {
  @apply bg-white/95 p-6 rounded-lg shadow-md border-l-4 border-gyaan-navy
         transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
}

/* Login Section */
.login-section {
  @apply relative overflow-hidden bg-gyaan-navy/95;
}

.fancy-heading-bg {
  @apply py-6 px-5 rounded-lg shadow-lg mb-3 relative overflow-hidden;
}

/* News Detail Page */
.prose {
  @apply max-w-none text-gray-800;
}

.prose p {
  @apply my-4 leading-relaxed;
}

.prose strong {
  @apply font-bold text-gyaan-navy;
}

.prose a {
  @apply text-gyaan-maroon hover:text-gyaan-navy underline;
}

.prose h2 {
  @apply text-xl font-bold text-gyaan-navy mt-8 mb-4;
}

.prose h3 {
  @apply text-lg font-bold text-gyaan-navy mt-6 mb-3;
}

.prose ul {
  @apply list-disc pl-6 my-4;
}

.prose ol {
  @apply list-decimal pl-6 my-4;
}

.prose li {
  @apply mb-2;
}

.about-card h3 {
  @apply font-extrabold;
}

.about-card p {
  @apply text-gray-800 font-medium;
}

/* Our Impact Section */
.impact-section {
  @apply relative overflow-hidden bg-gyaan-gold/10;
}

.impact-card {
  @apply bg-white/95 p-6 rounded-lg shadow-md border-b-4 border-gyaan-gold
         transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
}

.impact-card p {
  @apply font-extrabold;
}

/* How It Works Section */
.how-it-works-section {
  @apply relative overflow-hidden bg-gyaan-maroon/10;
}

.how-it-works-card {
  @apply bg-white/95 p-6 rounded-lg shadow-md border-t-4 border-gyaan-maroon
         transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
}

.how-it-works-card h3 {
  @apply font-extrabold;
}

.how-it-works-card p {
  @apply text-gray-800;
}

/* MoU Partners Section */
.partners-section {
  @apply relative overflow-hidden bg-gyaan-navy/10;
}

.partner-card {
  @apply bg-white/95 p-4 rounded-lg shadow-md border-r-4 border-gyaan-gold
         transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl;
}

.partner-card p {
  @apply font-bold text-gyaan-navy;
}

/* Section transitions */
.section-transition-top {
  @apply absolute top-0 left-0 right-0 h-16 -mt-16 z-0 opacity-20 bg-white;
}

.section-transition-bottom {
  @apply absolute bottom-0 left-0 right-0 h-16 -mb-16 z-0 opacity-20 bg-white;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .hover\:glow-button:hover,
  .btn-glow-gold,
  .btn-glow-navy,
  .btn-glow-maroon,
  .hover\:-translate-y-1:hover,
  .hover\:-translate-y-2:hover,
  .hover\:scale-105:hover,
  .group-hover\:scale-110,
  .group-hover\:text-gyaan-navy\/80,
  .group-hover\:text-gyaan-maroon\/80,
  .group-hover\:text-gyaan-gold\/90,
  .group-hover\:text-gyaan-gold\/80,
  .hover\:shadow-xl,
  .hover\:shadow-2xl,
  .fancy-heading,
  .fancy-heading-accent::before,
  .fancy-heading-dot,
  .section-heading-impact-partners,
  .impact-partners-heading-dot,
  .impact-partners-heading-accent::before,
  .animate-on-scroll,
  .animate-slide-in,
  .animate-fade-in,
  .animate-subtle-pulse,
  /* Registration form specific animations */
  .focus-within\:-translate-y-1,
  .group-hover\:text-gyaan-maroon,
  .transition-all,
  .transition-colors {
    animation: none !important;
    transform: none !important;
    transition: none !important;
  }

  /* Show scroll animations immediately for reduced motion users */
  .animate-on-scroll {
    opacity: 1 !important;
  }

  /* Static shadows for reduced motion users */
  .btn-glow-gold,
  .btn-glow-gold:hover {
    box-shadow: 0 0 8px 0 rgba(213, 160, 33, 0.5) !important;
    border-color: #d5a021 !important;
  }

  .btn-glow-navy,
  .btn-glow-navy:hover {
    box-shadow: 0 0 8px 0 rgba(10, 36, 99, 0.5) !important;
    border-color: #0a2463 !important;
  }

  .btn-glow-maroon,
  .btn-glow-maroon:hover {
    box-shadow: 0 0 8px 0 rgba(123, 8, 40, 0.5) !important;
    border-color: #7b0828 !important;
  }

  /* Legacy support */
  .hover\:glow-button:hover {
    background-color: #0a2463 !important; /* gyaan-navy */
    border-color: #d5a021 !important; /* gyaan-gold */
    border-width: 3px !important;
    box-shadow: 0 0 8px 0 rgba(213, 160, 33, 0.5) !important;
  }

  /* Show fancy heading elements immediately for reduced motion users */
  .fancy-heading {
    opacity: 1 !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
  }

  .fancy-heading-accent::before {
    width: 100% !important;
    opacity: 1 !important;
    box-shadow: 0 0 5px rgba(213, 160, 33, 0.5) !important;
  }

  .fancy-heading-dot {
    opacity: 1 !important;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3) !important;
  }

  /* Show impact partners heading elements immediately for reduced motion users */
  .section-heading-impact-partners {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  .impact-partners-heading-dot {
    opacity: 1 !important;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.3) !important;
  }

  .impact-partners-heading-accent::before {
    width: 100% !important;
    opacity: 1 !important;
    box-shadow: 0 0 5px rgba(213, 160, 33, 0.5) !important;
  }
}
