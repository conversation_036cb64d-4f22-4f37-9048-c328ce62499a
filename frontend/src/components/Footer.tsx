
import React from 'react';
import { NavLink } from 'react-router-dom';

const Footer = () => {
  return (
    <footer className="bg-gyaan-navy text-white pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {/* Organization Info */}
          <div>
            <h4 className="text-xl font-bold mb-4">GyaanRaksha Samyog</h4>
            <p className="text-gray-300 mb-4 text-sm">
              Empowering Protection Through Knowledge.
              Your digital gateway to accredited training courses from India's premier security institutes.
            </p>
            <p className="text-sm text-gray-300">
              <span className="sanskrit-quote">ज्ञानेन रक्षा, विकसित भारतम्</span>
            </p>
          </div>
          
          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-bold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <NavLink to="/" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  Home
                </NavLink>
              </li>
              <li>
                <NavLink to="/about" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  About RRU
                </NavLink>
              </li>
              <li>
                <NavLink to="/news" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  News & Announcements
                </NavLink>
              </li>
              
              <li>
                <NavLink to="/contact" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  Contact Us
                </NavLink>
              </li>
            </ul>
          </div>
          
          {/* User Access */}
          <div>
            <h4 className="text-xl font-bold mb-4">User Access</h4>
            <ul className="space-y-2">
              <li>
                <NavLink to="/register" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  Candidate Registration
                </NavLink>
              </li>
              <li>
                <NavLink to="/login" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  Candidate Login
                </NavLink>
              </li>
              <li>
                <NavLink to="/admin" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  Admin Login
                </NavLink>
              </li>
              <li>
                <NavLink to="/helpdesk" className="text-gray-300 hover:text-gyaan-gold transition-colors">
                  Helpdesk
                </NavLink>
              </li>
            </ul>
          </div>
          
          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-bold mb-4">Contact Us</h4>
            <address className="not-italic text-gray-300 text-sm space-y-2">
              <p>Rashtriya Raksha University</p>
              <p>Lavad, Gandhinagar</p>
              <p>Gujarat, India - 382305</p>
              <p className="pt-2">
                <a href="tel:+************" className="hover:text-gyaan-gold transition-colors">
                  Phone: +91 12345 67890
                </a>
              </p>
              <p>
                <a href="mailto:<EMAIL>" className="hover:text-gyaan-gold transition-colors">
                  Email: <EMAIL>
                </a>
              </p>
            </address>
          </div>
        </div>
        
        {/* Copyright */}
        <div className="border-t border-gray-700 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-400">
              &copy; {new Date().getFullYear()} GyaanRaksha Samyog. All rights reserved.
            </p>
            <div className="mt-4 md:mt-0">
              <ul className="flex space-x-6">
                <li>
                  <a href="#" className="text-gray-400 hover:text-gyaan-gold transition-colors text-sm">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-gyaan-gold transition-colors text-sm">
                    Terms of Use
                  </a>
                </li>
                <li>
                  <a href="#" className="text-gray-400 hover:text-gyaan-gold transition-colors text-sm">
                    Sitemap
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
