
import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { Button } from "@/components/ui/button";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const closeMenu = () => setIsMenuOpen(false);

  return (
    <header className="bg-amber-200 shadow-md w-full z-50">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <NavLink to="/" className="flex items-center" onClick={closeMenu}>
              <span className="text-2xl font-bold text-gyaan-navy">GyaanRaksha</span>
              <span className="text-2xl font-bold text-gyaan-maroon">Samyog</span>
            </NavLink>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <NavLink
              to="/"
              className={({isActive}) =>
                `text-sm font-medium ${isActive ? 'text-gyaan-maroon' : 'text-gyaan-navy hover:text-gyaan-maroon'}`
              }
            >
              Home
            </NavLink>
            <NavLink
              to="/about"
              className={({isActive}) =>
                `text-sm font-medium ${isActive ? 'text-gyaan-maroon' : 'text-gyaan-navy hover:text-gyaan-maroon'}`
              }
            >
              About RRU
            </NavLink>
            <NavLink
              to="/news"
              className={({isActive}) =>
                `text-sm font-medium ${isActive ? 'text-gyaan-maroon' : 'text-gyaan-navy hover:text-gyaan-maroon'}`
              }
            >
              News & Announcements
            </NavLink>
            <NavLink
              to="/contact"
              className={({isActive}) =>
                `text-sm font-medium ${isActive ? 'text-gyaan-maroon' : 'text-gyaan-navy hover:text-gyaan-maroon'}`
              }
            >
              Contact Us
            </NavLink>
            {/* <NavLink
              to="/courses"
              className={({isActive}) =>
                `text-sm font-medium ${isActive ? 'text-gyaan-maroon' : 'text-gyaan-navy hover:text-gyaan-maroon'}`
              }
            >
              Courses
            </NavLink> */}
            <div className="border-l border-gray-300 h-6 mx-2"></div>
            <NavLink to="/register">
              <Button variant="navy">
                Register
              </Button>
            </NavLink>
            <NavLink to="/login">
              <Button variant="outline" glow="navy" className="border-gyaan-navy text-gyaan-navy hover:bg-gyaan-navy/10">
                Login
              </Button>
            </NavLink>
          </nav>

          {/* Mobile menu button */}
          <button className="md:hidden" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-amber-200 border-t border-amber-300 animate-fade-in">
          <div className="container mx-auto px-4 py-3 flex flex-col space-y-4">
            <NavLink
              to="/"
              className={({isActive}) =>
                `px-3 py-2 rounded-md ${isActive ? 'bg-gyaan-navy/10 text-gyaan-navy font-medium' : 'text-gray-700 hover:bg-gray-100'}`
              }
              onClick={closeMenu}
            >
              Home
            </NavLink>
            <NavLink
              to="/about"
              className={({isActive}) =>
                `px-3 py-2 rounded-md ${isActive ? 'bg-gyaan-navy/10 text-gyaan-navy font-medium' : 'text-gray-700 hover:bg-gray-100'}`
              }
              onClick={closeMenu}
            >
              About RRU
            </NavLink>
            <NavLink
              to="/news"
              className={({isActive}) =>
                `px-3 py-2 rounded-md ${isActive ? 'bg-gyaan-navy/10 text-gyaan-navy font-medium' : 'text-gray-700 hover:bg-gray-100'}`
              }
              onClick={closeMenu}
            >
              News & Announcements
            </NavLink>
            <NavLink
              to="/contact"
              className={({isActive}) =>
                `px-3 py-2 rounded-md ${isActive ? 'bg-gyaan-navy/10 text-gyaan-navy font-medium' : 'text-gray-700 hover:bg-gray-100'}`
              }
              onClick={closeMenu}
            >
              Contact Us
            </NavLink>
            <NavLink
              to="/courses"
              className={({isActive}) =>
                `px-3 py-2 rounded-md ${isActive ? 'bg-gyaan-navy/10 text-gyaan-navy font-medium' : 'text-gray-700 hover:bg-gray-100'}`
              }
              onClick={closeMenu}
            >
              Courses
            </NavLink>
            <div className="border-t border-gray-200 pt-4 flex flex-col space-y-3">
              <NavLink
                to="/register"
                className="w-full bg-gyaan-navy text-white py-2 px-4 rounded-md text-center"
                onClick={closeMenu}
              >
                Register
              </NavLink>
              <NavLink
                to="/login"
                className="w-full border border-gyaan-navy text-gyaan-navy py-2 px-4 rounded-md text-center"
                onClick={closeMenu}
              >
                Login
              </NavLink>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
