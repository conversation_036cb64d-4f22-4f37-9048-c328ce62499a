import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Loader2, Check, X, Info } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/providers/AuthProvider';
import { useNavigate, NavLink } from 'react-router-dom';
import { useMutation } from '@/hooks/use-api';
import { authAPI } from '@/services/api';
import { RegisterData } from '@/types/api';
import { getErrorMessage } from '@/utils/api-helpers';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

const formSchema = z
  .object({
    fullName: z.string().min(3, { message: 'Full name is required' }),
    dob: z.date({ required_error: 'Date of birth is required' }),
    email: z.string().email({ message: 'Invalid email address' }),
    mobile: z
      .string()
      .min(10, { message: 'Mobile number must be at least 10 digits' })
      .max(10),
    aadhaar: z
      .string()
      .min(12, { message: 'Aadhaar number must be 12 digits' })
      .max(12),
    password: z
      .string()
      .min(8, { message: 'Password must be at least 8 characters' })
      .max(50)
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+])[A-Za-z\d!@#$%^&*()_+]{8,}$/,
        {
          message:
            'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
        }
      ),
    confirmPassword: z.string(),
    category: z.string({ required_error: 'Please select a category' }),
    passwordStrength: z.number().min(0).max(100).optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const RegistrationForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [maskedAadhaar, setMaskedAadhaar] = useState('');
  const { toast } = useToast();
  const { register } = useAuth();
  const navigate = useNavigate();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: '',
      email: '',
      mobile: '',
      aadhaar: '',
      password: '',
      confirmPassword: '',
      category: '',
      dob: undefined,
      passwordStrength: 0,
    },
  });

  // Calculate password strength
  const calculatePasswordStrength = (password: string): number => {
    if (!password) return 0;

    let strength = 0;

    // Length check
    if (password.length >= 8) strength += 20;
    if (password.length >= 12) strength += 10;

    // Character variety checks
    if (/[a-z]/.test(password)) strength += 10;
    if (/[A-Z]/.test(password)) strength += 15;
    if (/[0-9]/.test(password)) strength += 15;
    if (/[^A-Za-z0-9]/.test(password)) strength += 15;

    // Complexity checks
    if (/[a-z].*[a-z].*[a-z]/.test(password)) strength += 5;
    if (/[A-Z].*[A-Z]/.test(password)) strength += 5;
    if (/[0-9].*[0-9]/.test(password)) strength += 5;
    if (/[^A-Za-z0-9].*[^A-Za-z0-9]/.test(password)) strength += 5;

    return Math.min(strength, 100);
  };

  // Handle password change to update strength
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'password') {
        const strength = calculatePasswordStrength(value.password as string);
        setPasswordStrength(strength);
        form.setValue('passwordStrength', strength);
      }

      if (name === 'aadhaar') {
        const aadhaarValue = value.aadhaar as string;
        if (aadhaarValue && aadhaarValue.length > 0) {
          // Mask the Aadhaar number (show only last 4 digits)
          const masked = aadhaarValue.replace(/\d(?=\d{4})/g, 'X');
          setMaskedAadhaar(masked);
        } else {
          setMaskedAadhaar('');
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Use the useMutation hook for registration
  const { mutate: registerMutation, isLoading: isRegistering } = useMutation(
    (data: RegisterData) => authAPI.register(data),
    {
      onSuccess: (data) => {
        toast({
          title: 'Registration Successful',
          description:
            'Your account has been created successfully. Please check your email and verify your mobile number.',
        });

        // Reset form
        form.reset();

        // Navigate to mobile verification page with the mobile number
        navigate('/verify-mobile', {
          state: {
            mobile: form.getValues().mobile,
            message:
              'Please enter the OTP sent to your mobile number to complete verification.',
          },
        });
      },
      onError: (error) => {
        const errorMessage = getErrorMessage(
          error,
          'Registration failed. Please try again.'
        );

        toast({
          title: 'Registration Failed',
          description: errorMessage,
          variant: 'destructive',
        });
      },
    }
  );

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    // Format date of birth to ISO string
    const dobIso = values.dob.toISOString();

    // Prepare registration data
    const userData: RegisterData = {
      fullName: values.fullName,
      email: values.email,
      mobile: values.mobile,
      password: values.password,
      aadhaar: values.aadhaar,
      category: values.category,
      dob: dobIso,
      passwordStrength: passwordStrength,
    };

    // Call the register mutation
    registerMutation(userData);
  };

  return (
    <div className="w-full animate-fade-in">
      <Card className="shadow-xl overflow-hidden transition-all duration-300 hover:shadow-2xl">
        <CardHeader className="bg-gyaan-navy/95 text-white rounded-t-lg">
          <CardTitle className="text-2xl font-bold animate-slide-in">
            Candidate Registration
          </CardTitle>
          <CardDescription
            className="text-gray-200 animate-slide-in"
            style={{ animationDelay: '100ms' }}
          >
            Fill in your details to create an account
          </CardDescription>
        </CardHeader>
        <CardContent className="p-8 bg-white">
          <div
            className="flex items-center gap-2 p-3 bg-gyaan-navy/20 text-gyaan-navy rounded-md mb-6 border border-gyaan-navy/30 animate-slide-in"
            style={{ animationDelay: '200ms' }}
          >
            <Info className="h-5 w-5" />
            <p className="text-sm font-medium">
              All fields marked with * are mandatory
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Personal Information Section */}
              <div className="bg-white/50 p-4 rounded-lg border border-gyaan-navy/20 shadow-sm">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4 pb-2 border-b border-gyaan-gold/40">
                  Personal Information
                </h3>

                {/* Full Name and DOB - Side by side */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-x-6 gap-y-4">
                  <FormField
                    control={form.control}
                    name="fullName"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Full Name *
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your full name"
                            {...field}
                            className="border-gyaan-gold/65 focus:border-gyaan-gold shadow-sm focus:shadow-md transition-all duration-300 bg-white/80"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="dob"
                    render={({ field }) => (
                      <FormItem className="flex flex-col transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Date of Birth *
                        </FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  'w-full pl-3 text-left font-normal border-gyaan-maroon/65 bg-white/80 shadow-sm hover:shadow-md transition-all duration-300',
                                  !field.value && 'text-muted-foreground'
                                )}
                              >
                                {field.value ? (
                                  format(field.value, 'PPP')
                                ) : (
                                  <span>Pick a date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-auto p-0 animate-fade-in"
                            align="start"
                          >
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() ||
                                date < new Date('1940-01-01')
                              }
                              initialFocus
                              className="p-3 pointer-events-auto"
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Contact Information Section */}
              <div className="bg-white/50 p-4 rounded-lg border border-gyaan-navy/20 shadow-sm">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4 pb-2 border-b border-gyaan-gold/40">
                  Contact Information
                </h3>

                {/* Email and Mobile - Side by side */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-x-6 gap-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Email Address *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            {...field}
                            className="border-gyaan-navy/65 focus:border-gyaan-navy shadow-sm focus:shadow-md transition-all duration-300 bg-white/80"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="mobile"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Mobile Number * (for OTP verification)
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="10-digit mobile number"
                            {...field}
                            className="border-gyaan-gold/65 focus:border-gyaan-gold shadow-sm focus:shadow-md transition-all duration-300 bg-white/80"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Identification Section */}
              <div className="bg-white/50 p-4 rounded-lg border border-gyaan-navy/20 shadow-sm">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4 pb-2 border-b border-gyaan-gold/40">
                  Identification
                </h3>

                {/* Aadhaar and Category - Side by side */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-x-6 gap-y-4">
                  <FormField
                    control={form.control}
                    name="aadhaar"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Aadhaar Number *
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type="text"
                              placeholder="12-digit Aadhaar number"
                              {...field}
                              maxLength={12}
                              className="border-gyaan-maroon/65 focus:border-gyaan-maroon shadow-sm focus:shadow-md transition-all duration-300 bg-white/80"
                              onChange={(e) => {
                                // Only allow digits
                                const value = e.target.value.replace(/\D/g, '');
                                field.onChange(value);
                              }}
                            />
                            {maskedAadhaar && (
                              <div className="absolute top-0 left-0 w-full h-full flex items-center px-3 pointer-events-none">
                                <span className="text-gray-700">
                                  {maskedAadhaar}
                                </span>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormDescription className="text-gyaan-navy/80 text-xs">
                          Your Aadhaar number is stored securely and masked in
                          our system.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Category *
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="border-gyaan-navy/65 focus:border-gyaan-navy shadow-sm hover:shadow-md transition-all duration-300 bg-white/80">
                              <SelectValue placeholder="Select your category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="animate-fade-in">
                            <SelectItem value="state-police">
                              State Police
                            </SelectItem>
                            <SelectItem value="capf">CAPF</SelectItem>
                            <SelectItem value="civilian">
                              Civilian/Other
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Security Section */}
              <div className="bg-white/50 p-4 rounded-lg border border-gyaan-navy/20 shadow-sm">
                <h3 className="text-lg font-semibold text-gyaan-navy mb-4 pb-2 border-b border-gyaan-gold/40">
                  Account Security
                </h3>

                {/* Password and Confirm Password - Side by side */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-x-6 gap-y-4">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Create Password *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Create a strong password"
                            {...field}
                            className="border-gyaan-gold/65 focus:border-gyaan-gold shadow-sm focus:shadow-md transition-all duration-300 bg-white/80"
                          />
                        </FormControl>
                        <div className="mt-2">
                          <div className="flex justify-between mb-1">
                            <span className="text-xs font-medium text-gyaan-navy/80">
                              Password Strength
                            </span>
                            <span
                              className={cn(
                                'text-xs font-semibold',
                                passwordStrength < 30
                                  ? 'text-red-600'
                                  : passwordStrength < 60
                                  ? 'text-yellow-600'
                                  : passwordStrength < 80
                                  ? 'text-blue-600'
                                  : 'text-green-600'
                              )}
                            >
                              {passwordStrength < 30
                                ? 'Weak'
                                : passwordStrength < 60
                                ? 'Fair'
                                : passwordStrength < 80
                                ? 'Good'
                                : 'Strong'}
                            </span>
                          </div>
                          <Progress
                            value={passwordStrength}
                            className={cn(
                              'h-2 transition-all duration-500',
                              passwordStrength < 30
                                ? 'bg-red-200'
                                : passwordStrength < 60
                                ? 'bg-yellow-200'
                                : passwordStrength < 80
                                ? 'bg-blue-200'
                                : 'bg-green-200'
                            )}
                          />
                          <div className="flex flex-wrap gap-2 mt-2 text-xs">
                            <div className="flex items-center gap-1 transition-all duration-300">
                              {/[A-Z]/.test(field.value) ? (
                                <Check className="h-3 w-3 text-green-500" />
                              ) : (
                                <X className="h-3 w-3 text-red-500" />
                              )}
                              <span>Uppercase</span>
                            </div>
                            <div className="flex items-center gap-1 transition-all duration-300">
                              {/[a-z]/.test(field.value) ? (
                                <Check className="h-3 w-3 text-green-500" />
                              ) : (
                                <X className="h-3 w-3 text-red-500" />
                              )}
                              <span>Lowercase</span>
                            </div>
                            <div className="flex items-center gap-1 transition-all duration-300">
                              {/[0-9]/.test(field.value) ? (
                                <Check className="h-3 w-3 text-green-500" />
                              ) : (
                                <X className="h-3 w-3 text-red-500" />
                              )}
                              <span>Number</span>
                            </div>
                            <div className="flex items-center gap-1 transition-all duration-300">
                              {/[^A-Za-z0-9]/.test(field.value) ? (
                                <Check className="h-3 w-3 text-green-500" />
                              ) : (
                                <X className="h-3 w-3 text-red-500" />
                              )}
                              <span>Special</span>
                            </div>
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem className="transition-all duration-300 hover:-translate-y-1 focus-within:-translate-y-1 group">
                        <FormLabel className="text-gyaan-navy font-medium group-hover:text-gyaan-maroon transition-colors duration-300">
                          Confirm Password *
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="Confirm your password"
                            {...field}
                            className="border-gyaan-maroon/65 focus:border-gyaan-maroon shadow-sm focus:shadow-md transition-all duration-300 bg-white/80"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Submit Button Section */}
              <div
                className="bg-white/50 p-4 rounded-lg border border-gyaan-navy/20 shadow-sm animate-slide-in"
                style={{ animationDelay: '300ms' }}
              >
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                  <div className="text-sm text-gyaan-navy/80 bg-gyaan-navy/10 p-3 rounded-md border border-gyaan-navy/20 w-full sm:w-2/3">
                    <p className="font-medium">
                      By registering, you agree to our{' '}
                      <a
                        href="#"
                        className="text-gyaan-maroon hover:text-gyaan-gold transition-colors duration-300"
                      >
                        Terms of Service
                      </a>{' '}
                      and{' '}
                      <a
                        href="#"
                        className="text-gyaan-maroon hover:text-gyaan-gold transition-colors duration-300"
                      >
                        Privacy Policy
                      </a>
                      .
                    </p>
                  </div>
                  <Button
                    type="submit"
                    className="w-full sm:w-1/3 bg-gyaan-navy hover:bg-gyaan-navy/90 text-white shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 btn-glow-navy py-6"
                    disabled={isRegistering}
                  >
                    {isRegistering ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Registering...
                      </>
                    ) : (
                      'Register Account'
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="bg-gyaan-light p-4 border-t flex justify-between items-center">
          <p className="text-sm text-gyaan-navy/80 font-medium">
            Already have an account?
          </p>
          <NavLink to="/login">
            <Button
              variant="outline"
              className="border-gyaan-navy/65 text-gyaan-navy hover:bg-gyaan-navy/10 transition-all duration-300 hover:-translate-y-1 btn-glow-navy"
            >
              Login
            </Button>
          </NavLink>
        </CardFooter>
      </Card>
    </div>
  );
};

export default RegistrationForm;
