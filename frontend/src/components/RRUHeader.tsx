import { FC } from 'react';
import RRULogo from '@/assets/images/RRULogo.png';
import M<PERSON><PERSON>ogo from '@/assets/images/MHALogo.png';

const RRUHeader: FC = () => {
  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        {/* Left side - RRU Logo and Text */}
        <div className="flex items-center">
          <img 
            src={RRULogo} 
            alt="Rashtriya Raksha University Logo" 
            className="h-16 w-auto mr-4" 
          />
          <div>
            <h1 className="text-xl md:text-2xl font-serif font-bold text-blue-800">
              Rashtriya Raksha University
            </h1>
            <p className="text-sm text-blue-600">
              Pioneering National Security and Police University of India
            </p>
          </div>
        </div>

        {/* Right side - MHA Logo */}
        <div className="flex items-center">
          <div className="text-right mr-4 hidden md:block">
          </div>
          <img 
            src={MHALogo} 
            alt="Ministry of Home Affairs Logo" 
            className="h-16 w-auto" 
          />
        </div>
      </div>
    </header>
  );
};

export default RRUHeader;