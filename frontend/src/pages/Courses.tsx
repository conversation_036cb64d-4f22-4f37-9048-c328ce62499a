import React, { useState, useEffect } from 'react';
import { NavLink } from 'react-router-dom';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Clock, Users, BookOpen } from 'lucide-react';

// Mock course data
const coursesData = [
  {
    id: '1',
    title: 'Advanced Cybersecurity for Law Enforcement',
    category: 'cybersecurity',
    level: 'advanced',
    duration: '6 months',
    institute: 'School of Information Technology & Cybersecurity',
    seats: 30,
    description:
      'Comprehensive training on cybercrime investigation, digital forensics, and cyber threat intelligence for law enforcement professionals.',
    image: 'https://placehold.co/600x400/gyaan-navy/white?text=Cybersecurity',
  },
  {
    id: '2',
    title: 'Tactical Response and Crisis Management',
    category: 'security',
    level: 'intermediate',
    duration: '3 months',
    institute: 'School of Police Science & Security',
    seats: 40,
    description:
      'Training program focused on tactical response strategies, crisis management, and emergency response protocols.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Crisis+Management',
  },
  {
    id: '3',
    title: 'Forensic Science and Crime Scene Investigation',
    category: 'forensics',
    level: 'beginner',
    duration: '4 months',
    institute: 'School of Criminology & Behavioral Sciences',
    seats: 35,
    description:
      'Introduction to forensic science principles, crime scene investigation techniques, and evidence collection procedures.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Forensic+Science',
  },
  {
    id: '4',
    title: 'Counter-Terrorism Operations',
    category: 'security',
    level: 'advanced',
    duration: '8 months',
    institute: 'School of Internal Security & Strategic Studies',
    seats: 25,
    description:
      'Specialized training on counter-terrorism strategies, threat assessment, and preventive measures for security professionals.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Counter+Terrorism',
  },
  {
    id: '5',
    title: 'Police Leadership and Management',
    category: 'management',
    level: 'intermediate',
    duration: '5 months',
    institute: 'School of Management Studies',
    seats: 45,
    description:
      'Program designed to enhance leadership skills, management capabilities, and strategic thinking for police officers in leadership roles.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Police+Leadership',
  },
  {
    id: '6',
    title: 'Criminal Law and Procedure',
    category: 'law',
    level: 'beginner',
    duration: '3 months',
    institute: 'School of Law, Justice & Governance',
    seats: 50,
    description:
      'Comprehensive overview of criminal law, legal procedures, and judicial processes for law enforcement professionals.',
    image: 'https://placehold.co/600x400/gyaan-navy/white?text=Criminal+Law',
  },
];

const Courses = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [levelFilter, setLevelFilter] = useState('all');

  // Filter courses based on search term and filters
  const filteredCourses = coursesData.filter((course) => {
    const matchesSearch =
      course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory =
      categoryFilter === 'all' || course.category === categoryFilter;
    const matchesLevel = levelFilter === 'all' || course.level === levelFilter;

    return matchesSearch && matchesCategory && matchesLevel;
  });

  // Initialize animation for course cards
  useEffect(() => {
    // Function to handle scroll animations
    const handleScroll = () => {
      const animateItems = document.querySelectorAll('.animate-on-scroll');

      animateItems.forEach((item) => {
        const itemTop = item.getBoundingClientRect().top;
        const itemBottom = item.getBoundingClientRect().bottom;
        const windowHeight = window.innerHeight;

        if (itemTop < windowHeight - 100 && itemBottom > 0) {
          item.classList.add('animate-visible');
        }
      });
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);
    // Trigger once on load
    handleScroll();

    // Cleanup
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-gyaan-navy/95 text-white py-16 relative overflow-hidden">
          <div className="section-transition-top"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="fancy-heading-container">
              <div className="bg-gyaan-navy py-6 px-5 rounded-lg shadow-lg mb-3 relative overflow-hidden">
                {/* Background overlay with solid color */}
                <div className="absolute inset-0 bg-gyaan-navy/90 z-10"></div>
                <h2 className="fancy-heading relative z-20">
                  <span className="fancy-heading-accent">
                    Explore Our Courses
                  </span>
                </h2>
              </div>
              <div className="fancy-heading-decoration">
                <div className="fancy-heading-dot"></div>
                <div className="fancy-heading-dot"></div>
                <div className="fancy-heading-dot"></div>
              </div>
            </div>
            <div className="max-w-4xl mx-auto text-center mt-8 animate-fade-in">
              <p className="text-xl text-gray-200">
                Discover specialized training programs offered by Rashtriya
                Raksha University and its partner institutions.
              </p>
            </div>
          </div>
          <div className="section-transition-bottom"></div>
        </section>

        {/* Search and Filters */}
        <section className="py-10 bg-gyaan-light relative">
          <div className="section-transition-top"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              <div className="bg-white/95 p-6 rounded-lg shadow-md border-l-4 border-gyaan-navy animate-fade-in">
                <h3 className="text-xl font-bold text-gyaan-navy mb-6 relative pb-3">
                  Find Your Course
                  <span className="absolute bottom-0 left-0 w-16 h-1 bg-gyaan-maroon"></span>
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <div className="md:col-span-2">
                    <div className="relative group">
                      <Search
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-gyaan-navy transition-colors duration-300"
                        size={18}
                      />
                      <Input
                        placeholder="Search courses..."
                        className="pl-10 border-gyaan-navy/30 focus:border-gyaan-navy transition-all duration-300 hover:border-gyaan-navy/50"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                  </div>

                  <div>
                    <Select
                      value={categoryFilter}
                      onValueChange={setCategoryFilter}
                    >
                      <SelectTrigger className="border-gyaan-navy/30 hover:border-gyaan-navy/50 transition-all duration-300">
                        <SelectValue placeholder="Category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="cybersecurity">
                          Cybersecurity
                        </SelectItem>
                        <SelectItem value="security">Security</SelectItem>
                        <SelectItem value="forensics">Forensics</SelectItem>
                        <SelectItem value="management">Management</SelectItem>
                        <SelectItem value="law">Law</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Select value={levelFilter} onValueChange={setLevelFilter}>
                      <SelectTrigger className="border-gyaan-navy/30 hover:border-gyaan-navy/50 transition-all duration-300">
                        <SelectValue placeholder="Level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Levels</SelectItem>
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">
                          Intermediate
                        </SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="section-transition-bottom"></div>
        </section>

        {/* Courses List */}
        <section className="py-16 bg-gyaan-navy/95 relative">
          <div className="section-transition-top"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="fancy-heading-container mb-12">
              <div className="bg-gyaan-gold py-6 px-5 rounded-lg shadow-lg mb-3 relative overflow-hidden">
                {/* Background overlay with solid color */}
                <div className="absolute inset-0 bg-gyaan-gold/90 z-10"></div>
                <h2 className="fancy-heading relative z-20">
                  <span className="fancy-heading-accent">
                    Available Courses
                  </span>
                </h2>
              </div>
              <div className="fancy-heading-decoration">
                <div className="fancy-heading-dot"></div>
                <div className="fancy-heading-dot"></div>
                <div className="fancy-heading-dot"></div>
              </div>
            </div>

            <div className="max-w-6xl mx-auto">
              {filteredCourses.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {filteredCourses.map((course, index) => (
                    <div
                      key={course.id}
                      className="bg-white/95 rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-2 border-gyaan-gold/30 animate-on-scroll opacity-0"
                      style={{ animationDelay: `${0.1 * (index % 3)}s` }}
                    >
                      <div className="h-48 overflow-hidden relative group">
                        <img
                          src={course.image}
                          alt={course.title}
                          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-black/40 opacity-70"></div>
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <div className="flex flex-wrap gap-2">
                            <Badge className="bg-gyaan-navy shadow-md">
                              {course.category}
                            </Badge>
                            <Badge className="bg-gyaan-maroon shadow-md">
                              {course.level}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-bold text-gyaan-navy mb-3 border-b-2 border-gyaan-navy/20 pb-2">
                          {course.title}
                        </h3>
                        <p className="text-gray-800 mb-4 font-medium">
                          {course.description}
                        </p>

                        <div className="flex flex-wrap gap-4 text-sm text-gray-700 mb-4 bg-gyaan-light/50 p-3 rounded-md">
                          <div className="flex items-center">
                            <Clock size={18} className="mr-2 text-gyaan-navy" />
                            <span>{course.duration}</span>
                          </div>
                          <div className="flex items-center">
                            <Users size={18} className="mr-2 text-gyaan-navy" />
                            <span>{course.seats} seats</span>
                          </div>
                          <div className="flex items-center">
                            <BookOpen
                              size={18}
                              className="mr-2 text-gyaan-navy"
                            />
                            <span className="text-xs">{course.institute}</span>
                          </div>
                        </div>

                        <NavLink to={`/courses/${course.id}`}>
                          <Button className="w-full bg-gyaan-navy hover:bg-gyaan-navy/90 btn-glow-navy py-5 relative group">
                            <span className="relative z-10">View Details</span>
                            <span className="absolute inset-0 rounded-md bg-gyaan-gold/0 group-hover:bg-gyaan-gold/10 transition-colors duration-300 z-0"></span>
                          </Button>
                        </NavLink>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 bg-white/90 rounded-lg shadow-md p-8 animate-fade-in">
                  <p className="text-gray-700 text-lg font-medium mb-4">
                    No courses found matching your criteria.
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4 border-gyaan-navy text-gyaan-navy hover:bg-gyaan-navy/10 btn-glow-navy"
                    onClick={() => {
                      setSearchTerm('');
                      setCategoryFilter('all');
                      setLevelFilter('all');
                    }}
                  >
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>
          </div>
          <div className="section-transition-bottom"></div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Courses;
