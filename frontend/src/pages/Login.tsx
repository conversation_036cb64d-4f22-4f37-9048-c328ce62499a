import React from 'react';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import LoginForm from '@/components/LoginForm';
import { NavLink } from 'react-router-dom';

const Login = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow login-section py-12">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-7xl mx-auto">
            {/* Fancy heading with decorative underline */}
            <div className="fancy-heading-container animate-fade-in">
              <div className="fancy-heading-bg bg-gyaan-navy/95">
                <h1 className="fancy-heading">
                  <span className="fancy-heading-accent">
                    Login to Your Account
                  </span>
                </h1>
              </div>
              <div className="fancy-heading-decoration">
                <div className="fancy-heading-dot"></div>
                <div className="fancy-heading-dot"></div>
                <div className="fancy-heading-dot"></div>
              </div>
            </div>

            <div className="flex justify-center mt-12">
              <div
                className="animate-fade-in"
                style={{ animationDelay: '0.3s' }}
              >
                <LoginForm />
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Login;
