import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, NavLink } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeft,
  Calendar,
  User,
  Tag,
  Share2,
  Bookmark,
  Facebook,
  Twitter,
  Linkedin,
  Mail,
  BookOpen,
  Award,
  FileText,
  Users,
  Newspaper,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';

// Mock news data - same as in News.tsx
const newsItems = [
  {
    id: 1,
    title: 'New Training Program for Cybersecurity Professionals Launched',
    category: 'programs',
    date: '2023-12-15',
    excerpt:
      'Rashtriya Raksha University launches a specialized training program for cybersecurity professionals in collaboration with leading tech companies.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Cybersecurity+Program',
    content: `
      <p>Rashtriya Raksha University (RRU) has launched a comprehensive training program for cybersecurity professionals, aimed at enhancing the capabilities of law enforcement agencies in tackling cyber threats.</p>

      <p>The program, developed in collaboration with leading technology companies, will cover advanced topics including digital forensics, malware analysis, network security, and cyber threat intelligence.</p>

      <p>"This initiative represents a significant step forward in our mission to strengthen India's cybersecurity infrastructure," said Dr. Rajesh Kumar, Program Director at RRU. "By combining academic expertise with industry best practices, we are creating a unique learning experience that will equip professionals with the skills needed to address evolving cyber threats."</p>

      <p>The six-month program will feature both theoretical and practical components, with hands-on laboratory sessions and real-world case studies. Participants will also have the opportunity to engage with industry experts through guest lectures and workshops.</p>

      <p>Applications for the inaugural batch are now open, with classes scheduled to commence in February 2024. The program is primarily targeted at serving police officers, defense personnel, and government officials working in cybersecurity roles.</p>

      <p>For more information about the program and application process, interested candidates can visit the GyaanRaksha Samyog portal or contact the admissions office at RRU.</p>
    `,
    author: 'Dr. Priya Sharma',
    authorRole: 'Communications Officer',
    relatedTags: [
      'cybersecurity',
      'training',
      'digital forensics',
      'law enforcement',
    ],
  },
  {
    id: 2,
    title: 'MoU Signed with Gujarat Police Academy',
    category: 'partnerships',
    date: '2023-11-28',
    excerpt:
      'Rashtriya Raksha University signs Memorandum of Understanding with Gujarat Police Academy to enhance training capabilities.',
    image: 'https://placehold.co/600x400/gyaan-navy/white?text=MoU+Signing',
    content: `
      <p>Rashtriya Raksha University (RRU) has signed a Memorandum of Understanding (MoU) with the Gujarat Police Academy, marking a significant collaboration aimed at enhancing the training capabilities of both institutions.</p>

      <p>The agreement, signed in a ceremony held at RRU's campus, establishes a framework for joint training programs, faculty exchange, and research initiatives in areas of mutual interest such as police modernization, forensic science, and security management.</p>

      <p>"This partnership represents a convergence of expertise and resources that will benefit both institutions," said Vice Chancellor of RRU. "By combining our strengths, we can develop more comprehensive and effective training programs for police personnel."</p>

      <p>The Director of Gujarat Police Academy echoed these sentiments, stating that the collaboration would help in creating a more skilled and professional police force equipped to handle contemporary challenges.</p>

      <p>Under the terms of the MoU, the institutions will jointly develop specialized courses, conduct research on emerging security challenges, and organize workshops and seminars for knowledge exchange. The agreement also provides for the sharing of infrastructure and technical resources.</p>

      <p>The first joint training program under this partnership is expected to be launched in January 2024, focusing on advanced investigation techniques and technology-enabled policing.</p>
    `,
    author: 'Amit Patel',
    authorRole: 'Public Relations Officer',
    relatedTags: [
      'partnership',
      'police training',
      'Gujarat Police',
      'collaboration',
    ],
  },
  {
    id: 3,
    title: 'International Conference on Security Studies Announced',
    category: 'events',
    date: '2023-11-10',
    excerpt:
      'Rashtriya Raksha University to host International Conference on Security Studies in March 2024.',
    image:
      'https://placehold.co/600x400/gyaan-navy/white?text=Conference+Announcement',
    content: `
      <p>Rashtriya Raksha University (RRU) has announced that it will host the International Conference on Security Studies (ICSS) from March 15-17, 2024. The three-day event will bring together academics, practitioners, and policymakers from around the world to discuss contemporary security challenges and innovative approaches to addressing them.</p>

      <p>The conference will feature keynote addresses by distinguished security experts, panel discussions, paper presentations, and workshops on various themes including national security, cybersecurity, terrorism, border management, and maritime security.</p>

      <p>"ICSS 2024 provides a platform for meaningful dialogue and knowledge exchange on critical security issues facing nations today," said Dr. Anand Kumar, Conference Chair and Professor at RRU. "We are looking forward to hosting delegates from diverse backgrounds and fostering collaborative approaches to security challenges."</p>

      <p>The conference will also include special sessions on emerging technologies in security and their implications for policy and practice. A pre-conference workshop on security research methodologies will be held on March 14 for research scholars and early-career professionals.</p>

      <p>Call for papers has been issued, with abstracts due by January 15, 2024. Selected papers will be published in a special issue of the Journal of Security Studies, an international peer-reviewed publication.</p>

      <p>Registration for the conference is now open, with early bird discounts available until January 31, 2024. For more information and registration details, visit the conference website at www.icss2024.rru.ac.in.</p>
    `,
    author: 'Dr. Anand Kumar',
    authorRole: 'Conference Chair',
    relatedTags: [
      'conference',
      'security studies',
      'international event',
      'academic',
    ],
  },
];

const NewsDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [newsItem, setNewsItem] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [relatedNews, setRelatedNews] = useState<any[]>([]);

  // Handle back button
  const handleBack = () => {
    navigate('/news');
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Get category color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'programs':
        return 'bg-gyaan-gold text-gyaan-navy';
      case 'partnerships':
        return 'bg-gyaan-maroon text-white';
      case 'events':
        return 'bg-gyaan-navy text-white';
      case 'admissions':
        return 'bg-gyaan-orange text-white';
      case 'research':
        return 'bg-gyaan-gold text-gyaan-navy';
      default:
        return 'bg-gyaan-navy text-white';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'programs':
        return <BookOpen className="h-4 w-4" />;
      case 'partnerships':
        return <Users className="h-4 w-4" />;
      case 'events':
        return <Calendar className="h-4 w-4" />;
      case 'admissions':
        return <FileText className="h-4 w-4" />;
      case 'research':
        return <Award className="h-4 w-4" />;
      default:
        return <Newspaper className="h-4 w-4" />;
    }
  };

  // Fetch news item
  useEffect(() => {
    const fetchNewsItem = async () => {
      if (!id) {
        navigate('/news');
        return;
      }

      setIsLoading(true);

      try {
        // Find the news item in our mock data
        const numericId = parseInt(id);
        const foundNewsItem = newsItems.find((item) => item.id === numericId);

        if (foundNewsItem) {
          setNewsItem(foundNewsItem);

          // Find related news (same category, excluding current item)
          const related = newsItems
            .filter(
              (item) =>
                item.id !== numericId &&
                item.category === foundNewsItem.category
            )
            .slice(0, 3);
          setRelatedNews(related);
        } else {
          toast({
            title: 'Not Found',
            description: "The news article you're looking for doesn't exist.",
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error fetching news item:', error);
        toast({
          title: 'Error',
          description: 'An error occurred while loading the news article.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchNewsItem();
  }, [id, navigate, toast]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <RRUHeader />
        <Header />
        <main className="flex-grow flex items-center justify-center">
          <div className="flex flex-col items-center justify-center min-h-[60vh]">
            <div className="h-12 w-12 rounded-full border-4 border-gyaan-navy border-t-transparent animate-spin mb-4"></div>
            <p className="text-gray-600 text-lg">Loading article...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (!newsItem) {
    return (
      <div className="min-h-screen flex flex-col">
        <RRUHeader />
        <Header />
        <main className="flex-grow">
          <div className="container mx-auto px-4 py-16">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-2xl font-bold text-gyaan-navy mb-4">
                Article Not Found
              </h1>
              <p className="text-gray-600 mb-8">
                The news article you're looking for doesn't exist or has been
                removed.
              </p>
              <Button
                onClick={handleBack}
                className="bg-gyaan-maroon hover:bg-gyaan-maroon/90 text-white px-6 py-6 text-lg font-medium rounded-lg shadow-lg transform hover:-translate-y-1 transition-all duration-300 btn-glow-maroon flex items-center"
              >
                <ArrowLeft className="h-5 w-5 mr-3" />
                Back to News
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-gyaan-navy/95 text-white py-16 relative">
          <div className="section-transition-top"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              <div className="w-full flex justify-start mb-8">
                <Button
                  onClick={handleBack}
                  className="bg-gyaan-maroon hover:bg-gyaan-maroon/90 text-white px-6 py-6 text-lg font-medium rounded-lg shadow-lg transform hover:-translate-y-1 transition-all duration-300 btn-glow-maroon flex items-center"
                >
                  <ArrowLeft className="h-5 w-5 mr-3" />
                  Back to News
                </Button>
              </div>
            </div>
          </div>
          <div className="section-transition-bottom"></div>
        </section>

        {/* Article Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <article className="bg-white/95 rounded-lg shadow-lg overflow-hidden border border-gyaan-gold/30 animate-fade-in">
                {/* Featured Image */}
                <div className="relative h-64 md:h-96 overflow-hidden">
                  <img
                    src={newsItem.image}
                    alt={newsItem.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-black/50"></div>

                  {/* Category Badge */}
                  <div className="absolute top-4 right-4">
                    <Badge
                      className={`${getCategoryColor(
                        newsItem.category
                      )} px-3 py-1.5 text-sm flex items-center gap-1.5`}
                    >
                      {getCategoryIcon(newsItem.category)}
                      <span className="capitalize">{newsItem.category}</span>
                    </Badge>
                  </div>
                </div>

                {/* Article Header */}
                <div className="p-6 md:p-8 border-b border-gray-200">
                  <h1 className="text-2xl md:text-3xl font-bold text-gyaan-navy mb-4">
                    {newsItem.title}
                  </h1>

                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gyaan-maroon" />
                      <span>{formatDate(newsItem.date)}</span>
                    </div>

                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-gyaan-maroon" />
                      <span>
                        {newsItem.author}, {newsItem.authorRole}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Article Content */}
                <div className="p-6 md:p-8">
                  <div
                    className="prose prose-lg max-w-none"
                    dangerouslySetInnerHTML={{ __html: newsItem.content }}
                  ></div>

                  {/* Tags */}
                  {newsItem.relatedTags && newsItem.relatedTags.length > 0 && (
                    <div className="mt-8 pt-6 border-t border-gray-200">
                      <div className="flex flex-wrap items-center gap-2">
                        <Tag className="h-4 w-4 text-gyaan-navy" />
                        <span className="text-sm font-medium text-gyaan-navy">
                          Tags:
                        </span>
                        {newsItem.relatedTags.map(
                          (tag: string, index: number) => (
                            <Badge
                              key={index}
                              variant="outline"
                              className="bg-gyaan-light hover:bg-gyaan-light/80 text-gyaan-navy border-gyaan-navy/30"
                            >
                              {tag}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                  )}

                  {/* Share Buttons */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex flex-wrap items-center gap-3">
                      <span className="text-sm font-medium text-gyaan-navy flex items-center">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share:
                      </span>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="rounded-full h-8 w-8 p-0 bg-blue-600 text-white hover:bg-blue-700"
                      >
                        <Facebook className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="rounded-full h-8 w-8 p-0 bg-sky-500 text-white hover:bg-sky-600"
                      >
                        <Twitter className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="rounded-full h-8 w-8 p-0 bg-blue-700 text-white hover:bg-blue-800"
                      >
                        <Linkedin className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="rounded-full h-8 w-8 p-0 bg-red-500 text-white hover:bg-red-600"
                      >
                        <Mail className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </article>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default NewsDetailPage;
