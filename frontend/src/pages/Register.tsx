import React from 'react';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import RegistrationForm from '@/components/RegistrationForm';

const Register = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow bg-gyaan-light/30 py-12">
        <div className="container mx-auto px-4">
          <div className="fancy-heading-container animate-fade-in">
            <div className="fancy-heading-bg bg-gyaan-navy/95">
              <h1 className="fancy-heading">
                <span className="fancy-heading-accent">
                  Candidate Registration
                </span>
              </h1>
            </div>
            <div className="fancy-heading-decoration">
              <div className="fancy-heading-dot"></div>
              <div className="fancy-heading-dot"></div>
              <div className="fancy-heading-dot"></div>
            </div>
          </div>

          {/* Grid layout for side-by-side content */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mt-8">
            {/* Registration Process - Left Side */}
            <div className="lg:col-span-4 order-1 lg:order-1">
              <div
                className="bg-white rounded-lg shadow-lg p-6 border border-gyaan-navy/20 animate-slide-in flex flex-col justify-between lg:sticky lg:top-4"
                style={{ animationDelay: '200ms' }}
              >
                <div className="mb-6">
                  <h2 className="text-xl font-semibold mb-4 text-gyaan-maroon relative pb-2 inline-block">
                    Registration Process
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gyaan-gold/65"></span>
                  </h2>
                  <p className="text-gyaan-navy/80 text-sm">
                    Follow these steps to complete your registration and start
                    your learning journey with GyaanRaksha Samyog.
                  </p>
                </div>
                <ol className="list-decimal list-inside space-y-3 text-gyaan-navy/90 mb-6">
                  <li className="transition-all duration-300 hover:-translate-y-1 pl-2">
                    Complete the registration form with your personal details
                  </li>
                  <li className="transition-all duration-300 hover:-translate-y-1 pl-2">
                    Verify your email address and mobile number
                  </li>
                  <li className="transition-all duration-300 hover:-translate-y-1 pl-2">
                    Log in to your account to complete your profile
                  </li>
                  <li className="transition-all duration-300 hover:-translate-y-1 pl-2">
                    Select courses and submit required documents
                  </li>
                  <li className="transition-all duration-300 hover:-translate-y-1 pl-2">
                    Pay applicable fees and start your learning journey
                  </li>
                </ol>
                <p className="text-sm text-gyaan-navy/80 bg-gyaan-navy/10 p-3 rounded-md border border-gyaan-navy/20">
                  For assistance with registration, please contact our helpdesk
                  at{' '}
                  <a
                    href="mailto:<EMAIL>"
                    className="text-gyaan-maroon font-medium hover:text-gyaan-gold transition-colors duration-300"
                  >
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>

            {/* Registration Form - Right Side */}
            <div className="lg:col-span-8 order-2 lg:order-2">
              <RegistrationForm />
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Register;
