import React, { useEffect, useRef } from 'react';
import Header from '@/components/Header';
import RRUHeader from '@/components/RRUHeader';
import Footer from '@/components/Footer';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Send,
  ArrowRight,
} from 'lucide-react';

const formSchema = z.object({
  name: z.string().min(2, { message: 'Name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  phone: z.string().min(10, { message: 'Valid phone number is required' }),
  subject: z.string().min(5, { message: 'Subject is required' }),
  message: z
    .string()
    .min(10, { message: 'Message must be at least 10 characters' }),
});

const Contact = () => {
  const { toast } = useToast();
  const formRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
    },
  });

  // Animation for elements when they come into view
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-visible');
          }
        });
      },
      { threshold: 0.1 }
    );

    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach((el) => observer.observe(el));

    return () => {
      animatedElements.forEach((el) => observer.unobserve(el));
    };
  }, []);

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      // This would normally be an API call
      console.log('Form values:', values);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      toast({
        title: 'Message Sent',
        description:
          'Thank you for contacting us. We will get back to you soon.',
      });

      form.reset();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send message. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <RRUHeader />
      <Header />

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative py-20 overflow-hidden">
          {/* Background with solid color */}
          <div className="absolute inset-0 bg-gyaan-navy/80 z-0"></div>

          {/* Decorative elements */}
          <div className="absolute inset-0 z-0">
            <div className="absolute top-20 left-10 w-32 h-32 rounded-full bg-white/10 backdrop-blur-sm"></div>
            <div className="absolute bottom-10 right-20 w-40 h-40 rounded-full bg-white/5 backdrop-blur-sm"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 rounded-full bg-white/10 backdrop-blur-sm"></div>
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white text-shadow-strong animate-on-scroll opacity-0 transform translate-y-4">
                Connect With Us
              </h1>
              <div
                className="w-24 h-1 bg-gyaan-gold mx-auto mb-6 animate-on-scroll opacity-0"
                style={{ animationDelay: '0.2s' }}
              ></div>
              <p
                className="text-xl text-white animate-on-scroll opacity-0"
                style={{ animationDelay: '0.4s' }}
              >
                We're here to help with any questions about GyaanRaksha Samyog's
                programs and initiatives.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Form and Information */}
        <section className="py-16 relative overflow-hidden">
          {/* Background with subtle pattern */}
          <div className="absolute inset-0 bg-slate-50 opacity-70 z-0"></div>
          <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiMzMzMiIGZpbGwtb3BhY2l0eT0iMC4wMiI+PHBhdGggZD0iTTM2IDM0djZoNnYtNmgtNnptNiA2djZoLTZ2LTZoNnptLTYtNnYtNmg2djZoLTZ6Ii8+PC9nPjwvZz48L3N2Zz4=')] opacity-50 z-0"></div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Form */}
                <div
                  ref={formRef}
                  className="animate-on-scroll opacity-0 transform translate-y-4"
                >
                  <div className="relative">
                    <h2 className="text-2xl md:text-3xl font-bold mb-8 text-gyaan-navy relative inline-block">
                      Send Us a Message
                      <span className="absolute bottom-0 left-0 w-full h-1 bg-gyaan-gold"></span>
                    </h2>
                  </div>

                  <div className="bg-white/80 backdrop-blur-sm p-8 rounded-lg shadow-lg border border-teal-100">
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="space-y-6"
                      >
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem className="group">
                              <FormLabel className="text-amber-700 font-medium">
                                Full Name
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter your full name"
                                  className="border-teal-200 focus-visible:ring-teal-400 transition-all duration-300 hover:-translate-y-1 group-hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="text-rose-500" />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem className="group">
                                <FormLabel className="text-amber-700 font-medium">
                                  Email
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter your email"
                                    className="border-teal-200 focus-visible:ring-teal-400 transition-all duration-300 hover:-translate-y-1 group-hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="text-rose-500" />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem className="group">
                                <FormLabel className="text-amber-700 font-medium">
                                  Phone Number
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter your phone number"
                                    className="border-teal-200 focus-visible:ring-teal-400 transition-all duration-300 hover:-translate-y-1 group-hover:shadow-md"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="text-rose-500" />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="subject"
                          render={({ field }) => (
                            <FormItem className="group">
                              <FormLabel className="text-amber-700 font-medium">
                                Subject
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter subject"
                                  className="border-teal-200 focus-visible:ring-teal-400 transition-all duration-300 hover:-translate-y-1 group-hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="text-rose-500" />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="message"
                          render={({ field }) => (
                            <FormItem className="group">
                              <FormLabel className="text-amber-700 font-medium">
                                Message
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Enter your message"
                                  className="min-h-[150px] border-teal-200 focus-visible:ring-teal-400 transition-all duration-300 hover:-translate-y-1 group-hover:shadow-md"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="text-rose-500" />
                            </FormItem>
                          )}
                        />

                        <Button
                          type="submit"
                          variant="navy"
                          className="w-full transition-all duration-300 hover:-translate-y-1 hover:shadow-lg group"
                        >
                          <span>Send Message</span>
                          <Send className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                        </Button>
                      </form>
                    </Form>
                  </div>
                </div>

                {/* Contact Information */}
                <div
                  ref={infoRef}
                  className="animate-on-scroll opacity-0 transform translate-y-4"
                  style={{ animationDelay: '0.2s' }}
                >
                  <div className="relative">
                    <h2 className="text-2xl md:text-3xl font-bold mb-8 text-gyaan-maroon relative inline-block">
                      Contact Information
                      <span className="absolute bottom-0 left-0 w-full h-1 bg-gyaan-gold"></span>
                    </h2>
                  </div>

                  <div className="bg-gyaan-maroon/90 p-8 rounded-lg shadow-lg text-white">
                    <div className="space-y-8">
                      <div className="flex items-start group">
                        <div className="bg-white/20 p-4 rounded-full mr-5 transition-all duration-300 group-hover:-translate-y-1 group-hover:shadow-lg group-hover:bg-white/30">
                          <MapPin className="h-7 w-7 text-amber-300" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-xl text-amber-300 mb-2">
                            Address
                          </h3>
                          <p className="text-white/90 leading-relaxed">
                            Rashtriya Raksha University
                            <br />
                            Lavad, Gandhinagar
                            <br />
                            Gujarat, India - 382305
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start group">
                        <div className="bg-white/20 p-4 rounded-full mr-5 transition-all duration-300 group-hover:-translate-y-1 group-hover:shadow-lg group-hover:bg-white/30">
                          <Phone className="h-7 w-7 text-amber-300" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-xl text-amber-300 mb-2">
                            Phone
                          </h3>
                          <p className="text-white/90">
                            <a
                              href="tel:+************"
                              className="hover:text-amber-200 transition-colors"
                            >
                              +91 12345 67890
                            </a>
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start group">
                        <div className="bg-white/20 p-4 rounded-full mr-5 transition-all duration-300 group-hover:-translate-y-1 group-hover:shadow-lg group-hover:bg-white/30">
                          <Mail className="h-7 w-7 text-amber-300" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-xl text-amber-300 mb-2">
                            Email
                          </h3>
                          <p className="text-white/90">
                            <a
                              href="mailto:<EMAIL>"
                              className="hover:text-amber-200 transition-colors"
                            >
                              <EMAIL>
                            </a>
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start group">
                        <div className="bg-white/20 p-4 rounded-full mr-5 transition-all duration-300 group-hover:-translate-y-1 group-hover:shadow-lg group-hover:bg-white/30">
                          <Clock className="h-7 w-7 text-amber-300" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-xl text-amber-300 mb-2">
                            Working Hours
                          </h3>
                          <p className="text-white/90 leading-relaxed">
                            Monday - Friday: 9:00 AM - 5:00 PM
                            <br />
                            Saturday: 9:00 AM - 1:00 PM
                            <br />
                            Sunday: Closed
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-10 pt-8 border-t border-white/20">
                      <h3 className="font-semibold text-xl text-amber-300 mb-5">
                        Connect With Us
                      </h3>
                      <div className="flex space-x-5">
                        <a
                          href="#"
                          className="bg-white/20 p-3 rounded-full transition-all duration-300 hover:-translate-y-2 hover:bg-white/30 hover:shadow-lg"
                        >
                          <Facebook className="h-6 w-6 text-white" />
                        </a>
                        <a
                          href="#"
                          className="bg-white/20 p-3 rounded-full transition-all duration-300 hover:-translate-y-2 hover:bg-white/30 hover:shadow-lg"
                        >
                          <Twitter className="h-6 w-6 text-white" />
                        </a>
                        <a
                          href="#"
                          className="bg-white/20 p-3 rounded-full transition-all duration-300 hover:-translate-y-2 hover:bg-white/30 hover:shadow-lg"
                        >
                          <Instagram className="h-6 w-6 text-white" />
                        </a>
                        <a
                          href="#"
                          className="bg-white/20 p-3 rounded-full transition-all duration-300 hover:-translate-y-2 hover:bg-white/30 hover:shadow-lg"
                        >
                          <Linkedin className="h-6 w-6 text-white" />
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Decorative Wave Divider */}
        <div className="relative h-24 mt-10 overflow-hidden">
          <div className="absolute bottom-0 left-0 w-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 1440 320"
              className="w-full h-auto"
            >
              <path
                fill="#7e22ce"
                fillOpacity="0.65"
                d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,250.7C960,235,1056,181,1152,165.3C1248,149,1344,171,1392,181.3L1440,192L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              ></path>
            </svg>
          </div>
          <div
            className="absolute bottom-0 left-0 w-full"
            style={{ marginBottom: '-5px' }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 1440 320"
              className="w-full h-auto"
            >
              <path
                fill="#0d9488"
                fillOpacity="0.65"
                d="M0,288L48,272C96,256,192,224,288,213.3C384,203,480,213,576,229.3C672,245,768,267,864,261.3C960,256,1056,224,1152,218.7C1248,213,1344,235,1392,245.3L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
              ></path>
            </svg>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Contact;
